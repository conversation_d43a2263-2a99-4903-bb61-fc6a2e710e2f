using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Mis.Agent.Localization;
using Mis.Agent.PluginSystem;

using Mis.Agent.Services;
using Mis.Shared.Interface;
using System;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Reflection;
using System.Threading.Tasks;
using System.Web.Services.Description;
using System.Windows.Forms;
using System.Xml.Linq;
using CultureChangedEventArgs = Mis.Shared.Interface.CultureChangedEventArgs;

namespace Mis.Agent
{
    public partial class AgentForm : Form
    {
        private IServiceProvider _serviceProvider;
        private IHost _webHost;
        private bool _isArabic = false;
        bool _notificationsEnabled;
        private ILogger<AgentForm> _logger;
        private HubConnection _hubConnection; // Added HubConnection field
        private readonly IConfiguration _configuration;

        public AgentForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = _serviceProvider.GetService<ILogger<AgentForm>>();
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _configuration = new ConfigurationBuilder()
             .SetBasePath(AppContext.BaseDirectory)
             .AddJsonFile("appsettings.json", optional: true)
             .Build();
            InitializeComponent();
            InitializeLocalization();
            tabControl1.SelectedIndexChanged += TabControl1_SelectedIndexChanged; // Attach the event handler
            _serviceProvider = serviceProvider;
            NotificationManager.NotificationEvent += OnNotificationReceived;
            GetNotificationEnabledSetting();
        }
        public void GetNotificationEnabledSetting()
        {
            var section = _configuration.GetSection("NotificationSettings");
            _notificationsEnabled = section.GetValue<bool?>("EnabeledNotification") ?? false;
        }
        private void InitializeLocalization()
        {
            // Subscribe to culture changes
            GlobalLocalizationService.CultureChanged += OnCultureChanged;

            // Set up NotificationManager localization delegate
            NotificationManager.GetLocalizedString = GlobalLocalizationService.GetString;

            // Apply initial localization
            ApplyLocalization();
        }

        private void OnCultureChanged(object? sender, Localization.CultureChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged(sender, e)));
                return;
            }

            ApplyLocalization();
            ApplyRtlLayout(e.IsRightToLeft);

            // Notify all plugin forms about culture change
            NotificationManager.NotifyAllOfCultureChange();
        }

        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
        public void ShowNotification(string title, string text)
        {
            // Use centralized NotificationManager for consistent localization
            NotificationManager.SetNotificationEnabled(_notificationsEnabled);
            NotificationManager.ShowNotification(title, text);
        }
        private void ApplyRtlLayout(bool isRtl)
        {
            if (isRtl)
            {
                RightToLeft = RightToLeft.Yes;
                RightToLeftLayout = true;
            }
            else
            {
                RightToLeft = RightToLeft.No;
                RightToLeftLayout = false;
            }

            // Apply RTL to all child controls
            ApplyRtlToControls(this.Controls, isRtl);
        }

        private void ApplyRtlToControls(Control.ControlCollection controls, bool isRtl)
        {
            foreach (Control control in controls)
            {
                if (control is TabControl tabControl)
                {
                    tabControl.RightToLeft = isRtl ? RightToLeft.Yes : RightToLeft.No;
                    tabControl.RightToLeftLayout = isRtl;

                    foreach (TabPage tabPage in tabControl.TabPages)
                    {
                        tabPage.RightToLeft = isRtl ? RightToLeft.Yes : RightToLeft.No;
                        ApplyRtlToControls(tabPage.Controls, isRtl);
                    }
                }
                else
                {
                    control.RightToLeft = isRtl ? RightToLeft.Yes : RightToLeft.No;
                }

                if (control.HasChildren)
                {
                    ApplyRtlToControls(control.Controls, isRtl);
                }
            }
        }



        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_webHost != null)
            {
                _webHost.StopAsync();
                _webHost.Dispose();
            }
            base.OnFormClosing(e);
        }
        private async void TabControl1_SelectedIndexChanged(object? sender, EventArgs e)
        {
            try
            {
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred while refreshing data: {ex.Message}");
            }
        }


        /// <summary>
        /// Refreshes the notifications DataGridView with the latest data from the plugin system
        /// </summary>
        private async void RefreshDataGridView()
        {
            await RefreshNotificationsDataGridViewAsync(tabControl1);
        }

        /// <summary>
        /// Professional method to refresh notifications DataGridView using plugin system
        /// </summary>
        /// <param name="tabControl">The TabControl containing the notifications tab</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task RefreshNotificationsDataGridViewAsync(TabControl tabControl)
        {
            try
            {
                // Request notification data via the plugin system (no direct references)
                var notifications = await NotificationManager.RequestNotificationDataAsync();

                // Find the notifications tab
                if (tabControl.TabPages["NotificationsTab"] is not TabPage notificationsTab)
                {
                    Console.WriteLine("NotificationsTab not found in TabControl.");
                    return;
                }

                // Find the DataGridView control
                if (notificationsTab.Controls["dataGridView1"] is not DataGridView dataGridView)
                {
                    Console.WriteLine("dataGridView1 not found in NotificationsTab.");
                    return;
                }

                // Update the DataGridView on the UI thread
                if (dataGridView.InvokeRequired)
                {
                    dataGridView.Invoke(new Action(() => UpdateDataGridViewData(dataGridView, notifications)));
                }
                else
                {
                    UpdateDataGridViewData(dataGridView, notifications);
                }

                Console.WriteLine($"Successfully refreshed notifications DataGridView with {notifications.Count()} records.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error refreshing notifications DataGridView: {ex.Message}");
                MessageBox.Show($"Error refreshing notifications data: {ex.Message}",
                    "Notification Refresh Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// Updates the DataGridView with new notification data
        /// </summary>
        /// <param name="dataGridView">The DataGridView to update</param>
        /// <param name="notifications">The notification data</param>
        private static void UpdateDataGridViewData(DataGridView dataGridView, IEnumerable<TransactionDto> notifications)
        {
            try
            {
                dataGridView.DataSource = notifications.ToList();
                dataGridView.Refresh();

                // Optional: Auto-resize columns for better display
                if (dataGridView.Columns.Count > 0)
                {
                    dataGridView.AutoResizeColumns(DataGridViewAutoSizeColumnsMode.AllCells);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating DataGridView data: {ex.Message}");
            }
        }

        private async void AgentForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Load plugin tabs (plugins are already initialized by AgentStarter)
                await LoadPluginTabs();

                // Apply initial localization
                ApplyLocalization();
            }
            catch (Exception ex)
            {
                ShowNotification("Error", $"Error loading application: {ex.Message}");
            }
        }
        /// <summary>
        /// Load plugin tabs from already initialized plugins
        /// </summary>
        private async Task LoadPluginTabs()
        {
            try
            {
                // Get the plugin manager instance (plugins should already be loaded by AgentStarter)
                var pluginManager = PluginManager.Instance;

                if (pluginManager.LoadedPlugins.Count == 0)
                {
                    ShowNotification("Warning", "No plugins are loaded. Please restart the application.");
                    return;
                }

                Console.WriteLine($"Loading tabs from {pluginManager.LoadedPlugins.Count} already loaded plugins");

                // Clear existing tabs to prevent duplicates
                tabControl1.TabPages.Clear();

                // Add plugin tab pages to the form
                var tabPages = pluginManager.GetTabPages();
                Console.WriteLine($"Found {tabPages.Count} tab pages from plugins");

                foreach (var tabPage in tabPages)
                {
                    // Ensure tab has proper name and text
                    if (string.IsNullOrEmpty(tabPage.Text))
                    {
                        // Set default text based on plugin name or tab name
                        tabPage.Text = GetLocalizedTabName(tabPage.Name);
                    }

                    Console.WriteLine($"Adding tab page: Name='{tabPage.Name}', Text='{tabPage.Text}'");
                    tabControl1.TabPages.Add(tabPage);
                }

                Console.WriteLine($"Total tabs in control: {tabControl1.TabPages.Count}");

                // Apply localization to all tabs
                LocalizeTabPages();

                // Debug tab information
                for (int i = 0; i < tabControl1.TabPages.Count; i++)
                {
                    var tab = tabControl1.TabPages[i];
                    Console.WriteLine($"Tab {i}: Text='{tab.Text}', Name='{tab.Name}', Visible={tab.Visible}");
                }

                // Force refresh the tab control
                tabControl1.Refresh();
            }
            catch (Exception ex)
            {
                ShowNotification("Error", $"Error loading plugin tabs: {ex.Message}");
                Console.WriteLine($"Error loading plugin tabs: {ex.Message}");
            }

            await Task.CompletedTask;
        }
        /// <summary>
        /// Gets localized tab name based on tab name or plugin type
        /// </summary>
        private string GetLocalizedTabName(string tabName)
        {
            // Map tab names to localized strings
            return tabName switch
            {
                "PrintTab" => GlobalLocalizationService.GetString("PrintTab", "Print"),
                "BarcodeTab" => GlobalLocalizationService.GetString("BarcodeTab", "Barcode"),
                "ScannerTab" => GlobalLocalizationService.GetString("ScannerTab", "Scanner"),
                "PortTab" => GlobalLocalizationService.GetString("PortTab", "Port"),
                "NotificationsTab" => GlobalLocalizationService.GetString("NotificationsTab", "Notifications"),
                _ => tabName ?? "Unknown"
            };
        }



        private void ApplyLocalization()
        {
            // Update form title
            this.Text = AgentResources.FormTitle;

            // Update buttons
            btnSaveAllSettings.Text = AgentResources.BtnSaveAllSettings_Text;

            // Update language switch button text based on current language
            if (GlobalLocalizationService.CurrentCulture.Name == "ar-SA")
            {
                btnSwitchLanguage.Text = AgentResources.SwitchToEnglish;
            }
            else
            {
                btnSwitchLanguage.Text = AgentResources.SwitchToArabic;
            }

            // Update tab pages
            LocalizeTabPages();
        }

        private void LocalizeTabPages()
        {
            foreach (TabPage tab in tabControl1.TabPages)
            {
                // Use direct resource access for tab localization
                switch (tab.Name)
                {
                    case "PrintTab":
                        tab.Text = AgentResources.PrintTab;
                        break;
                    case "BarcodeTab":
                        tab.Text = AgentResources.BarcodeTab;
                        break;
                    case "ScannerTab":
                        tab.Text = AgentResources.ScannerTab;
                        break;
                    case "PortTab":
                        tab.Text = AgentResources.PortTab;
                        break;
                    case "NotificationsTab":
                        tab.Text = AgentResources.NotificationsTab;
                        break;
                    default:
                        // Fallback to GlobalLocalizationService for unknown tabs
                        string localizedText = GlobalLocalizationService.GetString(tab.Name);
                        if (localizedText != tab.Name)
                        {
                            tab.Text = localizedText;
                        }
                        break;
                }
            }
        }

        private void BtnSwitchLanguage_Click(object sender, EventArgs e)
        {
            try
            {
                // Toggle language
                string newCulture = GlobalLocalizationService.CurrentCulture.Name == "ar-SA" ? "en-US" : "ar-SA";
                GlobalLocalizationService.SetCulture(newCulture);

                // Update the flag
                _isArabic = newCulture == "ar-SA";

                // Show notification using localized keys
                NotificationManager.ShowNotification("LanguageSwitched", "LanguageSwitchedMessage", "Language Switched", "Language has been switched successfully.");
            }
            catch (Exception ex)
            {
                NotificationManager.ShowNotification("Error", "ErrorMessage", "Error", ex.Message);
            }
        }









        /// <summary>
        /// Centralized save all settings functionality
        /// </summary>
        /// 
        private async void btnSaveAllSettings_Click(object sender, EventArgs e)
        {
            await SaveAllSettingsAsync();
        }
        private async Task SaveAllSettingsAsync()
        {
            try
            {
                var settingsService = new CentralizedSettingsService();

                // Get current snapshot
                var existingSnapshot = settingsService.LoadSettingsFromAppsettings();
                var allSettings = await CollectAllPluginSettings(existingSnapshot);

                // Check which tabs actually changed
                var changedSettings = allSettings
                    .Where(tab =>
                        tab.Value is Dictionary<string, object> newTab &&
                        existingSnapshot.TryGetValue(tab.Key, out var existingObj) &&
                        existingObj is Dictionary<string, object> oldTab &&
                        HasSettingsChanged(newTab, oldTab)
                    )
                    .ToDictionary(tab => tab.Key, tab => tab.Value);

                // ? If nothing changed, don't validate or save
                if (!changedSettings.Any())
                {
                    ShowNotification(AgentResources.UpdateNotification, AgentResources.SettingsUpToDate);
                    return;
                }

                // ?? CHECK FOR EXTRACTION ERRORS
                foreach (var tabSettings in allSettings.Values)
                {
                    if (tabSettings is Dictionary<string, object> dict &&
                        dict.ContainsKey("HasError") && (bool)dict["HasError"])
                    {
                        ShowNotification(dict["ErrorTitle"].ToString(), dict["ErrorMessage"].ToString());
                        return;
                    }

                }
                // Check if any settings require application restart
                bool requiresRestart = await CheckIfRestartRequired(allSettings);

                // Validate settings before saving
                var validationResult = ValidateAllSettings(allSettings);
                if (!validationResult.IsValid)
                {
                    ShowNotification("Validation Error", validationResult.ErrorMessage);
                    return;
                }

                // Save all settings to appsettings.json
                await settingsService.SaveAllSettingsAsync(allSettings);

                // Apply immediate settings that don't require restart
                await ApplyImmediateSettings(allSettings);

                // Show success notification

                if (requiresRestart)
                {

                    ShowNotification(AgentResources.AgentSuccess, AgentResources.RestartRequired);

                    Console.WriteLine("Settings require restart - restarting application in 2 seconds...");

                    // Restart application after a short delay
                    await Task.Delay(2000);
                    RestartApplication();
                }
                else
                {
                    ShowNotification(AgentResources.AgentSuccess, AgentResources.AllSettingsSaved);
                    Console.WriteLine("Settings saved successfully - no restart required.");
                }

                // Perform notification cleanup after saving settings
                await PerformNotificationCleanupAsync();

            }
            catch (Exception ex)
            {
                string errorTitle = AgentResources.Error;
                string errorMessage = AgentResources.ErrorSavingSettings;
                ShowNotification(errorTitle, errorMessage);
            }
        }

        /// <summary>
        /// Performs notification cleanup using the plugin system via NotificationManager
        /// </summary>
        private async Task PerformNotificationCleanupAsync()
        {
            try
            {
                // Request cleanup via the static NotificationManager (no plugin references needed)
                NotificationManager.RequestCleanup();
                Console.WriteLine("Notification cleanup requested via NotificationManager.");

                // Add a small delay to allow the async cleanup to start
                await Task.Delay(100);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error requesting notification cleanup: {ex.Message}");
                // Don't throw the exception to avoid breaking the save process
            }
        }

        private bool HasSettingsChanged(Dictionary<string, object> newSettings, Dictionary<string, object> oldSettings)
        {
            foreach (var kvp in newSettings)
            {
                if (!oldSettings.TryGetValue(kvp.Key, out var oldValue))
                    return true;

                if (!object.Equals(kvp.Value, oldValue))
                    return true;
            }

            return false;
        }




        /// <summary>
        /// Checks if any settings require application restart
        /// </summary>
        private async Task<bool> CheckIfRestartRequired(Dictionary<string, object> allSettings)
        {
            try
            {
                var settingsService = new CentralizedSettingsService();

                // Check if Port URL has changed
                if (allSettings.ContainsKey("PortTab"))
                {
                    var portSettings = allSettings["PortTab"] as Dictionary<string, object>;
                    if (portSettings?.ContainsKey("BaseUrl") == true)
                    {
                        var newUrl = portSettings["BaseUrl"]?.ToString();
                        var currentUrl = settingsService.GetSetting<string>("Server", "BaseUrl", "");

                        if (!string.IsNullOrEmpty(newUrl) && newUrl != currentUrl)
                        {
                            Console.WriteLine($"Port URL changed from '{currentUrl}' to '{newUrl}' - restart required");
                            return true;
                        }
                    }
                }

                // Check if Barcode URL has changed
                if (allSettings.ContainsKey("BarcodeTab"))
                {
                    var barcodeSettings = allSettings["BarcodeTab"] as Dictionary<string, object>;

                    // Check Barcode URL change
                    if (barcodeSettings?.ContainsKey("BarcodeBaseUrl") == true)
                    {
                        var newUrl = barcodeSettings["BarcodeBaseUrl"]?.ToString();
                        var currentUrl = settingsService.GetSetting<string>("Barcode", "BarcodeBaseUrl", "");

                        if (!string.IsNullOrEmpty(newUrl) && newUrl != currentUrl)
                        {
                            Console.WriteLine($"Barcode URL changed from '{currentUrl}' to '{newUrl}' - restart required");
                            return true;
                        }
                    }

                    // Check COM Port change
                    if (barcodeSettings?.ContainsKey("ComPort") == true)
                    {
                        var newComPort = barcodeSettings["ComPort"]?.ToString();
                        var currentComPort = settingsService.GetSetting<string>("Barcode", "ComPort", "");

                        if (!string.IsNullOrEmpty(newComPort) && newComPort != currentComPort)
                        {
                            Console.WriteLine($"COM Port changed from '{currentComPort}' to '{newComPort}' - restart required");
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error checking restart requirement: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Validates all settings before saving
        /// </summary>
        private ValidationResult ValidateAllSettings(Dictionary<string, object> allSettings)
        {
            try
            {
                // Validate Barcode settings
                if (allSettings.ContainsKey("BarcodeTab"))
                {
                    var barcodeSettings = allSettings["BarcodeTab"] as Dictionary<string, object>;

                    // Validate Barcode URL
                    if (barcodeSettings?.ContainsKey("BarcodeBaseUrl") == true)
                    {
                        var url = barcodeSettings["BarcodeBaseUrl"]?.ToString();
                        if (string.IsNullOrWhiteSpace(url))
                        {
                            return new ValidationResult(false, AgentResources.InvalidUrlMessage);
                        }
                    }

                    // Validate COM Port selection
                    //if (barcodeSettings?.ContainsKey("ComPort") == true)
                    //{
                    //    var comPort = barcodeSettings["ComPort"]?.ToString();
                    //    if (string.IsNullOrWhiteSpace(comPort))
                    //    {
                    //        _logger.LogInformation(AgentResources.NoPortSelectedMessage);
                    //        return new ValidationResult(false,AgentResources.NoPortSelectedMessage);
                    //    }
                    //}
                }

                // Validate Scanner settings
                if (allSettings.ContainsKey("ScannerTab"))
                {
                    var scannerSettings = allSettings["ScannerTab"] as Dictionary<string, object>;
                    if (scannerSettings?.ContainsKey("IsScanByBarcodeReader") == true)
                    {
                        var isScanByBarcodeReader = Convert.ToBoolean(scannerSettings["IsScanByBarcodeReader"]);
                        if (!isScanByBarcodeReader && !scannerSettings.ContainsKey("Scanner"))
                        {
                            return new ValidationResult(false, "Please select a scanner.");
                        }
                    }
                }

                // Validate Port settings
                if (allSettings.ContainsKey("PortTab"))
                {
                    var portSettings = allSettings["PortTab"] as Dictionary<string, object>;
                    if (portSettings?.ContainsKey("BaseUrl") == true)
                    {
                        var url = portSettings["BaseUrl"]?.ToString();
                        if (string.IsNullOrWhiteSpace(url))
                        {
                            return new ValidationResult(false, "Please enter a valid Port URL.");
                        }
                    }
                }

                return new ValidationResult(true, string.Empty);
            }
            catch (Exception ex)
            {
                return new ValidationResult(false, $"Validation error: {ex.Message}");
            }
        }



        /// <summary>
        /// Sets the default printer
        /// </summary>
        [System.Runtime.InteropServices.DllImport("winspool.drv", CharSet = System.Runtime.InteropServices.CharSet.Auto, SetLastError = true)]
        private static extern bool SetDefaultPrinter(string Name);
        /// <summary>
        /// Applies settings that can be changed immediately without restart
        /// </summary>
        private async Task ApplyImmediateSettings(Dictionary<string, object> allSettings)
        {
            try
            {
                // Apply printer settings immediately
                if (allSettings.ContainsKey("PrintTab"))
                {
                    var printSettings = allSettings["PrintTab"] as Dictionary<string, object>;
                    if (printSettings?.ContainsKey("DefaultPrinter") == true)
                    {
                        var printerName = printSettings["DefaultPrinter"]?.ToString();
                        if (!string.IsNullOrEmpty(printerName))
                        {
                            // Set as default printer
                            SetDefaultPrinter(printerName);
                        }
                    }
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error applying immediate settings: {ex.Message}");
            }
        }
        /// <summary>
        /// Restarts the application
        /// </summary>
        private void RestartApplication()
        {
            try
            {
                System.Diagnostics.Process.Start(Application.ExecutablePath);
                Application.Exit();
            }
            catch (Exception ex)
            {
                ShowNotification("Error", $"Failed to restart application: {ex.Message}");
            }
        }

        /// <summary>
        /// Collects settings from all loaded plugins
        /// </summary>

        private async Task<Dictionary<string, object>> CollectAllPluginSettings(Dictionary<string, object> existingSnapshot)
        {
            var allSettings = new Dictionary<string, object>();

            try
            {
                foreach (TabPage tabPage in tabControl1.TabPages)
                {
                    Dictionary<string, object> oldTabSettings = existingSnapshot.TryGetValue(tabPage.Name, out var val) && val is Dictionary<string, object> dict
                        ? dict
                        : new Dictionary<string, object>();

                    var settings = await ExtractSettingsFromTab(tabPage, oldTabSettings);
                    if (settings.Any())
                    {
                        allSettings[tabPage.Name] = settings;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error collecting plugin settings: {ex.Message}");
            }

            return allSettings;
        }

        /// <summary>
        /// Extracts settings from a specific tab page
        /// </summary>
        private async Task<Dictionary<string, object>> ExtractSettingsFromTab(TabPage tabPage, Dictionary<string, object> oldSettings)
        {
            return tabPage.Name switch
            {
                "PrintTab" => ExtractPrintSettings(tabPage),
                "BarcodeTab" => ExtractBarcodeSettings(tabPage, oldSettings),
                "ScannerTab" => ExtractScannerSettings(tabPage),
                "PortTab" => ExtractPortSettings(tabPage, oldSettings),
                "NotificationsTab" => ExtractNotificationsSettings(tabPage, oldSettings),
                _ => new Dictionary<string, object>()
            };
        }

        private Dictionary<string, object> ExtractNotificationsSettings(TabPage tabPage, Dictionary<string, object> oldSettings)
        {
            var settings = new Dictionary<string, object>();

            try
            {
                var enableNotifications = FindControlByName(tabPage, "enableNotificationsCheckBox") as CheckBox;
                var enableCleanup = FindControlByName(tabPage, "enableCleanupCheckBox") as CheckBox;
                var txtKeepDays = FindControlByName(tabPage, "txtKeepDays") as TextBox;
                var txtMaxRecords = FindControlByName(tabPage, "txtMaxRecords") as TextBox;

                settings["EnabeledNotification"] = enableNotifications?.Checked;

                var cleanupSettings = new Dictionary<string, object>
                {
                    ["Enabled"] = enableCleanup?.Checked ?? false
                };

                if (int.TryParse(txtKeepDays?.Text, out int keepDays))
                {
                    cleanupSettings["KeepDays"] = keepDays;
                }

                if (int.TryParse(txtMaxRecords?.Text, out int maxRecords))
                {
                    cleanupSettings["MaxRecords"] = maxRecords;
                }

                settings["NotificationCleanup"] = cleanupSettings;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extracting notification settings: {ex.Message}");
            }

            return settings;
        }


        private Dictionary<string, object> ExtractPrintSettings(TabPage tabPage)
        {
            var settings = new Dictionary<string, object>();

            try
            {
                var printerComboBox = FindControlByName(tabPage, "printerComboBox") as ComboBox;
                var paperSizeComboBox = FindControlByName(tabPage, "paperSizeComboBox") as ComboBox;

                if (printerComboBox?.SelectedItem != null)
                {
                    settings["DefaultPrinter"] = printerComboBox.SelectedItem.ToString();
                }

                if (paperSizeComboBox?.SelectedItem != null)
                {
                    settings["DefaultPaperSize"] = paperSizeComboBox.SelectedItem.ToString();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extracting print settings: {ex.Message}");
            }

            return settings;
        }


        private Dictionary<string, object> ExtractScannerSettings(TabPage tabPage)
        {
            var settings = new Dictionary<string, object>();

            try
            {
                var comboBoxScanners = FindControlByName(tabPage, "comboBoxScanners") as ComboBox;
                var checkBoxUseBarcodeReader = FindControlByName(tabPage, "checkBoxUseBarcodeReader") as CheckBox;

                if (comboBoxScanners?.SelectedItem != null)
                {
                    settings["Scanner"] = comboBoxScanners.SelectedItem.ToString();
                }

                if (checkBoxUseBarcodeReader != null)
                {
                    settings["IsScanByBarcodeReader"] = checkBoxUseBarcodeReader.Checked;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error extracting scanner settings: {ex.Message}");
            }

            return settings;
        }

        private Dictionary<string, object> ExtractBarcodeSettings(TabPage tabPage, Dictionary<string, object> oldSettings)
        {
            var settings = new Dictionary<string, object> { ["HasError"] = false };

            try
            {
                var barcodeUrlTextBox = FindControlByName(tabPage, "barcodeUrlTextBox") as TextBox;
                var comboBoxCOMPorts = FindControlByName(tabPage, "comboBoxCOMPorts") as ComboBox;

                var barcodeUrl = barcodeUrlTextBox?.Text?.Trim();
                if (string.IsNullOrWhiteSpace(barcodeUrl))
                {
                    settings["HasError"] = true;
                    settings["ErrorTitle"] = AgentResources.BarcodeError;
                    settings["ErrorMessage"] = AgentResources.BarcodeUrlMissing;
                    return settings;
                }

                if (!Uri.TryCreate(barcodeUrl, UriKind.Absolute, out Uri barcodeUri))
                {
                    settings["HasError"] = true;
                    settings["ErrorTitle"] = AgentResources.BarcodeError;
                    settings["ErrorMessage"] = AgentResources.BarcodeUrlInvalid;
                    return settings;
                }

                // ���� ���� ���� ����� ���� ���� �� ������ �������� ��� ������
                if (oldSettings.TryGetValue("BarcodeBaseUrl", out var oldUrlObj) &&
                    Uri.TryCreate(oldUrlObj?.ToString(), UriKind.Absolute, out Uri oldUri))
                {
                    int oldPort = oldUri.Port;
                    int newPort = barcodeUri.Port;

                    if (newPort != oldPort) // ��� ��� ���� ������
                    {
                        if (IsPortUsedAnywhere(newPort) || IsPortUsedOnSpecificIp(newPort, barcodeUri.Host))
                        {
                            settings["HasError"] = true;
                            settings["ErrorTitle"] = AgentResources.BarcodeError;
                            settings["ErrorMessage"] = string.Format(AgentResources.PortInUse, newPort);
                            return settings;
                        }
                    }
                }
                else
                {
                    // �� ���� ����� ���� ��� ������ ���� ����
                    int newPort = barcodeUri.Port;
                    if (IsPortUsedAnywhere(newPort) || IsPortUsedOnSpecificIp(newPort, barcodeUri.Host))
                    {
                        settings["HasError"] = true;
                        settings["ErrorTitle"] = AgentResources.BarcodeError;
                        settings["ErrorMessage"] = string.Format(AgentResources.PortInUse, newPort);
                        return settings;
                    }
                }

                settings["BarcodeBaseUrl"] = barcodeUrl;

                if (comboBoxCOMPorts?.SelectedItem != null)
                {
                    settings["ComPort"] = comboBoxCOMPorts.SelectedItem.ToString();
                }
                //else
                //{
                //    settings["HasError"] = true;
                //    settings["ErrorTitle"] = AgentResources.BarcodeError;
                //    settings["ErrorMessage"] = AgentResources.ComPortMissing;
                //}
            }
            catch (Exception ex)
            {
                settings["HasError"] = true;
                settings["ErrorTitle"] = AgentResources.BarcodeError;
                settings["ErrorMessage"] = $"��� �� ������� ������� ��������: {ex.Message}";
            }

            // ��� ������ ����� ��� �� ���� ���
            if (settings.ContainsKey("HasError") && settings["HasError"] is bool hasError && !hasError)
            {
                settings.Remove("HasError");
                settings.Remove("ErrorTitle");
                settings.Remove("ErrorMessage");
            }

            return settings;
        }

        private Dictionary<string, object> ExtractPortSettings(TabPage tabPage, Dictionary<string, object> oldSettings)
        {
            var settings = new Dictionary<string, object> { ["HasError"] = false };

            try
            {
                var AgentUrlField = FindControlByName(tabPage, "AgentUrlField") as TextBox;
                var AgentPortField = FindControlByName(tabPage, "AgentPortField") as TextBox;

                string hostInput = AgentUrlField?.Text?.Trim();
                string portText = AgentPortField?.Text?.Trim();

                if (string.IsNullOrWhiteSpace(portText) || !int.TryParse(portText, out int port) || port < 1 || port > 65535)
                {
                    settings["HasError"] = true;
                    settings["ErrorTitle"] = AgentResources.PortError;
                    settings["ErrorMessage"] = AgentResources.InvalidPortFormat;
                    return settings;
                }

                // ���� ������ ������ ����� ������ ������ ���� ������
                if (oldSettings.TryGetValue("BaseUrl", out var oldUrlObj) &&
                    Uri.TryCreate(oldUrlObj?.ToString(), UriKind.Absolute, out Uri oldUri))
                {
                    int oldPort = oldUri.Port;
                    if (port != oldPort)
                    {
                        if (IsPortUsedAnywhere(port) || (hostInput != null && IsPortUsedOnSpecificIp(port, hostInput)))
                        {
                            settings["HasError"] = true;
                            settings["ErrorTitle"] = AgentResources.PortError;
                            settings["ErrorMessage"] = string.Format(AgentResources.PortInUse, port);
                            return settings;
                        }
                    }
                }
                else
                {
                    if (IsPortUsedAnywhere(port) || (hostInput != null && IsPortUsedOnSpecificIp(port, hostInput)))
                    {
                        settings["HasError"] = true;
                        settings["ErrorTitle"] = AgentResources.PortError;
                        settings["ErrorMessage"] = string.Format(AgentResources.PortInUse, port);
                        return settings;
                    }
                }

                if (string.IsNullOrWhiteSpace(hostInput))
                {
                    settings["HasError"] = true;
                    settings["ErrorTitle"] = AgentResources.PortError;
                    settings["ErrorMessage"] = AgentResources.BarcodeUrlMissing;
                    return settings;
                }

                if (!hostInput.StartsWith("http://") && !hostInput.StartsWith("https://"))
                    hostInput = "http://" + hostInput;

                var finalUrl = new UriBuilder(hostInput) { Port = port }.ToString();
                settings["BaseUrl"] = finalUrl;
                settings["RequiresRestart"] = true;
            }
            catch (Exception ex)
            {
                settings["HasError"] = true;
                settings["ErrorTitle"] = AgentResources.PortError;
                settings["ErrorMessage"] = $"��� �� ������� ������� ������: {ex.Message}";
            }

            // ��� ������ ����� ��� �� ���� ���
            if (settings.ContainsKey("HasError") && settings["HasError"] is bool hasError && !hasError)
            {
                settings.Remove("HasError");
                settings.Remove("ErrorTitle");
                settings.Remove("ErrorMessage");
            }

            return settings;
        }

        private static bool IsPortUsedAnywhere(int port)
        {
            var ipGlobalProperties = IPGlobalProperties.GetIPGlobalProperties();
            var activeListeners = ipGlobalProperties.GetActiveTcpListeners();
            return activeListeners.Any(endpoint => endpoint.Port == port);
        }

        private static bool IsPortUsedOnSpecificIp(int port, string host)
        {
            try
            {
                IPAddress ip;

                if (!IPAddress.TryParse(host, out ip))
                {
                    ip = IPAddress.Loopback;
                }
                using var listener = new TcpListener(ip, port);
                listener.Start();
                listener.Stop();
                return false;
            }
            catch (SocketException)
            {
                return true;
            }
        }

        private Control FindControlByName(Control container, string name)
        {
            if (container.Name == name)
                return container;

            foreach (Control control in container.Controls)
            {
                var found = FindControlByName(control, name);
                if (found != null)
                    return found;
            }

            return null;
        }






    }

    /// Validation result for settings
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }

        public ValidationResult(bool isValid, string errorMessage)
        {
            IsValid = isValid;
            ErrorMessage = errorMessage;
        }
    }

}
